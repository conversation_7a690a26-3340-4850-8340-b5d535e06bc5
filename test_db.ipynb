import pandas as pd
from DatabaseManagement.ImportExport import get_table_from_GZ
from IP.Trademarks_Bulk.trademark_db import get_table_from_db

def find_missing_trademarks():
    """Find trademarks that exist in tb_case but are missing from PostgreSQL database"""

    # Get cases from GZ database
    print("Fetching cases from GZ database...")
    df = get_table_from_GZ("tb_case", force_refresh=True)

    # Get existing trademarks from PostgreSQL database (much more efficient than individual queries)
    print("Fetching existing trademarks from PostgreSQL database...")
    db_trademarks_df = get_table_from_db("trademarks")

    # Create a set of existing ser_no values for fast lookup
    existing_ser_nos = set()
    if not db_trademarks_df.empty:
        existing_ser_nos = set(db_trademarks_df['ser_no'].dropna().astype(str))

    print(f"Found {len(existing_ser_nos)} existing trademarks in database")

    # Initialize counters
    missing_count = 0
    missing_trademarks = []

    print("Processing case data...")
    # Process each case
    for _, row in df.iterrows():
        if not row['images'] or not isinstance(row['images'], dict):
            continue

        # Get trademarks from images
        trademarks = row['images'].get('trademarks', {})
        if not trademarks:
            continue

        # Process each trademark
        for image_name, trademark_info in trademarks.items():
            if not isinstance(trademark_info, dict):
                continue

            # Get serial numbers - handle both single values and arrays
            ser_numbers = trademark_info.get('ser_no', [])
            if not isinstance(ser_numbers, list):
                ser_numbers = [ser_numbers] if ser_numbers else []

            # Get registration numbers - handle both single values and arrays
            reg_numbers = trademark_info.get('reg_no', [])
            if not isinstance(reg_numbers, list):
                reg_numbers = [reg_numbers] if reg_numbers else []

            # Process each serial number
            for ser_no in ser_numbers:
                if not ser_no:  # Skip empty/null serial numbers
                    continue

                # Clean up serial number
                ser_no = str(ser_no).strip()
                if not ser_no:
                    continue

                # Check if trademark exists in database (fast set lookup)
                if ser_no not in existing_ser_nos:
                    missing_count += 1
                    missing_trademarks.append({
                        'plaintiff_id': row.get('plaintiff_id', 'Unknown'),
                        'docket': row.get('docket', 'Unknown'),
                        'ser_no': ser_no,
                        'reg_no': reg_numbers[0] if reg_numbers else None,
                        'trademark_text': trademark_info.get('trademark_text', ''),
                        'int_cls_list': trademark_info.get('int_cls_list', []),
                        'image_filename': image_name
                    })

                    # Print progress for large datasets
                    if missing_count % 100 == 0:
                        print(f"Found {missing_count} missing trademarks so far...")

    print(f"\nTotal missing trademarks: {missing_count}")

    # Save missing trademarks to CSV
    if missing_trademarks:
        df_missing = pd.DataFrame(missing_trademarks)
        output_file = 'missing_trademarks.csv'
        df_missing.to_csv(output_file, index=False)
        print(f"Missing trademarks have been saved to {output_file}")
        print("\nSample of missing trademarks:")
        print(df_missing.head(10))

        # Print some statistics
        print(f"\nStatistics:")
        print(f"- Total missing: {len(df_missing)}")
        print(f"- Unique plaintiff_ids: {df_missing['plaintiff_id'].nunique()}")
        print(f"- Unique dockets: {df_missing['docket'].nunique()}")
    else:
        print("No missing trademarks found!")

    return missing_trademarks

if __name__ == "__main__":
    find_missing_trademarks()


import os
import sys

from DatabaseManagement.ImportExport import get_table_from_GZ
from Common.Constants import sanitize_name

def print_copyright_filenames_with_page():
    df = get_table_from_GZ("tb_case", force_refresh=True)
    missing_count = 0
    case_type = "trademarks"

    for index, row in df.iterrows():
        if row['images'] and isinstance(row['images'], dict) and case_type in row['images']:
            docket_sanitized = row['docket'].replace(':', '_')
            if row['images'][case_type] and isinstance(row['images'][case_type], dict):
                for image in row['images'][case_type].keys():
                    reg_no = row['images'][case_type][image].get('reg_no', '')
                    ser_no = row['images'][case_type][image].get('ser_no', '')
                    SELECT from trademarks where ser_no = ser_no
                    if notfound: 
                        missing_count += 1
                        add(row("plaintiff_id"), reg_no, ser_no)

    print(f"\nTotal number missing: {missing_count}")

if __name__ == "__main__":
    print_copyright_filenames_with_page()

import pandas as pd
from pprint import pprint
import json

pd.set_option('display.max_columns', None)

from DatabaseManagement.ImportExport import get_table_from_GZ

cases_df = get_table_from_GZ("tb_case", force_refresh=True)

cases_df[cases_df['docket']=='1:24-cv-00514']
1:23-cv-02556
1:22-cv-04646
2:23-cv-01327
5:23-cv-04093

cases_df.dtypes

cases_df[pd.isna(cases_df['nos_description'])]

cases_df[
    pd.isna(cases_df['nos_description']) &
    (pd.to_datetime(cases_df['date_filed']) >= '2025-01-01') &
    (pd.to_datetime(cases_df['date_filed']) < '2025-05-01')
]

cases_df.sort_values(by='create_time', ascending=False)

cases_df[cases_df['id']==14728]

from DatabaseManagement.ImportExport import get_table_from_GZ
trademark_df = get_table_from_GZ("tb_trademark", force_refresh="true")

trademark_df.columns

trademark_df



# target_reg_no = '0993356'
target_reg_no = '6450672'

for index, row in cases_df.iterrows():
    case_id = row.get('id')
    images_data = row.get('images', {})
    plaintiff_id = row.get('plaintiff_id')
    if isinstance(images_data, str):
        try:
            images_data = json.loads(images_data)
        except:
            continue

    trademarks = images_data.get('trademarks', {})

    for tm_filename, tm_data in trademarks.items():
        reg_nos = tm_data.get('reg_no', [])
        if not isinstance(reg_nos, list):
            reg_nos = [reg_nos]

        if target_reg_no in reg_nos:
            print(f"Found reg_no '{target_reg_no}' in case ID: {case_id}, trademark file: {tm_filename} and plaintiff_id: {plaintiff_id}")

cases_df[cases_df['id']==1146]

cases_df[cases_df['id']==3398]

cases_df[cases_df['id']==4652]

cases_df[cases_df['id']==14606]

dat = {'full_filename': ['90024030_full.webp',
                   '90401978_full.webp',
                   '88755465_full.webp',
                   '88776760_full.webp',
                   '88478808_full.webp',
                   '77520844_full.webp',
                   '85099353_full.webp',
                   '77954386_full.webp',
                   '85100120_full.webp',
                   '85100166_full.webp',
                   '88547050_full.webp',
                   '90024670_full.webp',
                   '88479398_full.webp',
                   '88544991_full.webp',
                   '90400497_full.webp',
                   '88546469_full.webp',
                   '88543510_full.webp',
                   '88546469_full.webp',
                   '90401978_full.webp',
                   '88547050_full.webp',
                   '77520844_full.webp',
                   '90024030_full.webp',
                   '77954386_full.webp',
                   '88755465_full.webp',
                   '85100120_full.webp',
                   '88776760_full.webp',
                   '88479398_full.webp',
                   '88478808_full.webp',
                   '85099353_full.webp',
                   '85100166_full.webp',
                   '90400497_full.webp',
                   '90024670_full.webp',
                   '88543510_full.webp',
                   '88544991_full.webp',
                   '97899334_full.webp',
                   '98031070_full.webp',
                   '90029799_full.webp'],
 'int_cls_list': [18, 28, 14, 16, 25, 9, 26, 21, 41],
 'reg_no': ['6450672',
            '6486041',
            '6165896',
            '6274074',
            '5949916',
            '3755545',
            '3911256',
            '3863617',
            '3911258',
            '3911259',
            '6329556',
            '6256277',
            '5954392',
            '6201534',
            '6470809',
            '6143308',
            '6031011',
            '6143308',
            '6486041',
            '6329556',
            '3755545',
            '6450672',
            '3863617',
            '6165896',
            '3911258',
            '6274074',
            '5954392',
            '5949916',
            '3911256',
            '3911259',
            '6470809',
            '6256277',
            '6031011',
            '6201534',
            '7385897',
            '7387948',
            '7049434'],
 'ser_no': ['90024030',
            '90401978',
            '88755465',
            '88776760',
            '88478808',
            '77520844',
            '85099353',
            '77954386',
            '85100120',
            '85100166',
            '88547050',
            '90024670',
            '88479398',
            '88544991',
            '90400497',
            '88546469',
            '88543510',
            '88546469',
            '90401978',
            '88547050',
            '77520844',
            '90024030',
            '77954386',
            '88755465',
            '85100120',
            '88776760',
            '88479398',
            '88478808',
            '85099353',
            '85100166',
            '90400497',
            '90024670',
            '88543510',
            '88544991',
            '97899334',
            '98031070',
            '90029799'],
 'trademark_text': 'THE TEXAS CHAINSAW MASSACRE'}

for key, value in dat.items():
    if isinstance(value, list):
        total_items = len(value)
        unique_items = len(set(value))
        duplicates = [item for item in set(value) if value.count(item) > 1]
        
        print(f"Key: {key}")
        print(f" - Total items: {total_items}")
        print(f" - Unique items: {unique_items}")
        print(f" - Number of duplicates: {total_items - unique_items}")
        print(f" - Duplicate values: {duplicates}\n")
    else:
        print(f"Key: {key} is not a list (value: {value})\n")



cases_with_trademarks = []
for index, row in cases_df.iterrows():
        images_data = row.get('images', {})
        if images_data and 'trademarks' in images_data and images_data['trademarks']:
            cases_with_trademarks.append(index)

cases_df.loc[cases_with_trademarks]

cases_df[cases_df.duplicated(subset=['docket', 'court'], keep='last')]

cases_df[cases_df['docket']=='1:25-cv-08488']

cases_df[cases_df['id'].isin([14612, 14613, 14614, 14615, 14611])]

cases_df.columns

if not cases_df.at[8267, 'aisummary'] or cases_df.at[8267, 'aisummary'] == "":
    print("True")
else:
    print("False")

if cases_df.at[8267, 'aisummary'] is None or cases_df.at[8267, 'aisummary'] == "":
    print("True")
else:
    print("False")

if pd.isna(cases_df.at[8267, 'aisummary']) or cases_df.at[8267, 'aisummary'] == "":
    print("True")

if pd.isna(cases_df.at[idx, 'aisummary']) or cases_df.at[idx, 'aisummary'] == "":

cases_df[cases_df['docket'] == '1:25-cv-08481']

ddff.loc[[8180]]

ddff.loc[[8180]]['images'].values[0]['trademarks']

ids_to_remove = [14416, 14425, 14422, 14420, 14419, 14415, 14421, 14423, 14418, 14417, 14424, 14426]
all_cases_df = all_cases_df[~all_cases_df['id'].isin(ids_to_remove)]

len(all_cases_df)

all_cases_df.head(1)

all_cases_df[all_cases_df['docket'] == '1:25-cv-05113']

fault_case = all_cases_df[all_cases_df['docket'] == '1:25-cv-05113']
fault_case

df = all_cases_df.loc[[8180]]

from Common.Constants import local_case_folder
from Common.Constants import sanitize_name
import os

def process_images_data(images_data):
    """Process images data from different formats"""
    if isinstance(images_data, str):
        return json.loads(images_data)
    return images_data if isinstance(images_data, dict) else {}


def get_case_dir(row):
    """Get case directory path"""
    return os.path.join(
        local_case_folder,
        sanitize_name(f"{row['date_filed'].strftime('%Y-%m-%d')} - {row['docket']}")
    )


def get_image_paths(images, case_dir, plaintiff_id):
    """Generate all image paths and keys"""
    # Reminder of the structure of the field: df["images"]["trademark"][filename]= {regno = "8765468468", full_filename = ["us_indef_pag5"]}
    for ip in images.keys():
        for image in images[ip].keys():
            paths = []
            # Actual images
            for res in ["high", "low"]:
                file_path = os.path.join(case_dir, "images", res, image)
                key = f'plaintiff_images/{int(plaintiff_id)}/{res}/{image}'
                paths.append((key, file_path))

            # Certificates images
            for full_image in images[ip][image]["full_filename"]:
                file_path = os.path.join(case_dir, "images", "high", full_image)
                key = f'plaintiff_images/{int(plaintiff_id)}/high/{full_image}'
                paths.append((key, file_path))

            yield image, paths

upload_tasks_paths = []
for index, row in df.iterrows():
    images = process_images_data(row["images"])
    # print(images)
    # break
    case_dir = get_case_dir(row)
    for image, paths in get_image_paths(images, case_dir, row["plaintiff_id"]):
            for key, file_path in paths:
                upload_tasks_paths.append((file_path, image))

upload_tasks_paths[]

ddff.loc[[8180]]['images'].values[0]['trademarks']['1_25-cv-08010_regno_6169993.webp']

upload_tasks_paths



(all_cases_df.at[8075, 'images_status'] or {})

all_cases_df

all_cases_df.head(10)

all_cases_df.tail(10)







import os
import requests

os.environ["LANGFUSE_SECRET_KEY"] = "******************************************"
os.environ["LANGFUSE_PUBLIC_KEY"] = "pk-lf-bac4b191-590d-4059-89d6-e5ff73e91b48"
os.environ["LANGFUSE_PROJECT_ID"]="cm7i3hojo001rm907nz1tzlea"
os.environ["LANGFUSE_HOST"]="http://***************:3000"

dataset_name = "CopyrightReports"
public_key = os.environ["LANGFUSE_PUBLIC_KEY"]
secret_key = os.environ["LANGFUSE_SECRET_KEY"]
host = os.environ["LANGFUSE_HOST"]

url = f"{host}/api/public/dataset-items"
params = {"datasetName": dataset_name}

response = requests.get(url, params=params, auth=(public_key, secret_key))

if response.status_code == 200:
    data = response.json()
else:
    print(f"Error {response.status_code}: {response.text}")

print(f"Dataset Name: {data['data'][0]['datasetName']}")
print(f"Dataset Id: {data['data'][0]['datasetId']}")    

for i, item in enumerate(data['data'], 1):
    print(f"Total dataset items: {len(data['data'])}")
    print(f"\nItem {i}:")
    print("input:", item.get("input"))
    print("expectedOutput:", item.get("expectedOutput"))
    print("="*100)

dataset_id = "cmcvscloo0004ru07za88db9n"
run_name = "Report_CopyrightSamreen"

url = f"{host}/api/public/datasets/{dataset_name}/runs"
response = requests.get(url, params=params, auth=(public_key, secret_key))

response.json()

dataset_id = "cmcvscloo0004ru07za88db9n"
run_name = "Copyright Experiment - Report_CopyrightSamreen"

url = f"{host}/api/public/dataset-run-items"
params = {
    "datasetId": dataset_id,
    "runName": run_name,
}

response = requests.get(url, params=params, auth=(public_key, secret_key))

response.json()

trace_id = "24adc44ab057082f4b09d0592b588ef1"  # First trace
params = {
    "limit": 1,  # Adjust the limit as needed
    "offset": 0   # Adjust the offset for pagination
}
url = f"{host}/api/public/traces/{trace_id}"

response = requests.get(url, auth=(public_key, secret_key))

import numpy as np

np.nan == np.nan

response.json()

url = f"{host}/api/public/observations"

response = requests.get(url, auth=(public_key, secret_key))

response.json()

problem_rows = []
nan_plaintiff_rows = []

def is_prob_reg(r):
    s = str(r or "")
    return (
        not s.isdigit()
        or len(s) <= 5
        or s == "0000000"
        or s.startswith("000")
    )

def is_prob_ser(sno):
    s = str(sno or "")
    return (
        not s.isdigit()
        or len(s) <= 5
        or s == "00000000"
        or s.startswith("000")
    )

for index, row in cases_df.iterrows():
    case_id = row.get('id')
    
    # Check for NaN plaintiff_id
    if pd.isna(row['plaintiff_id']):
        print(f"NaN plaintiff_id found in case ID: {case_id}")
        nan_plaintiff_rows.append(row)
        continue  # Skip processing if plaintiff_id is NaN

    images_data = row.get('images', {})
    
    try:
        if not isinstance(images_data, dict):
            images_data = json.loads(images_data or "{}")
    except Exception:
        print(f"Could not parse images data for case {case_id}")
        continue

    try:
        plaintiff_id = int(float(row['plaintiff_id']))
    except Exception as e:
        print(f"Invalid plaintiff_id in case {case_id}: {row['plaintiff_id']}")
        nan_plaintiff_rows.append(row)
        continue

    for tm_filename, tm_data in images_data.get('trademarks', {}).items():
        full_filenames = tm_data.get('full_filename', [])

        if not isinstance(full_filenames, list):
            full_filenames = [full_filenames]

        # Skip if any full_filename contains "dead"
        if any("dead" in fn.lower() for fn in full_filenames):
            continue

        reg_nos = tm_data.get('reg_no', [])
        ser_nos = tm_data.get('ser_no', [])

        if not isinstance(reg_nos, list):
            reg_nos = [reg_nos]
        if not isinstance(ser_nos, list):
            ser_nos = [ser_nos]

        bad_regs = [r for r in reg_nos if is_prob_reg(r)]
        bad_sers = [s for s in ser_nos if is_prob_ser(s)]

        if bad_regs or bad_sers:
            problem_rows.append({
                'case_id': case_id,
                'tm_filename': tm_filename,
                'bad_reg_nos': bad_regs,
                'bad_ser_nos': bad_sers,
                'plaintiff_id': plaintiff_id
            })

len(problem_rows), len(nan_plaintiff_rows)

plaintiff_df = pd.DataFrame(nan_plaintiff_rows)
plaintiff_df

problem_df = pd.DataFrame(problem_rows)
problem_df

cases_df[cases_df['id'] == 2045]

csvv = pd.read_csv("/app/Alerts/PicturesProcessing/migration_changes_log.csv")

csvv

df = cases_df[['id', 'plaintiff_id', 'images']]
df

def check_trademark_pattern(images_dict):
    if not isinstance(images_dict, dict):
        return None
    
    trademarks = images_dict.get('trademarks', {})
    if not trademarks:
        return None
    
    for filename, trademark_data in trademarks.items():
        # Check if filename ends with .webp
        if not filename.endswith('.webp'):
            return False
        
        # Check if all required fields exist
        required_fields = ['reg_no', 'ser_no', 'trademark_text', 'int_cls_list', 'full_filename']
        if not all(field in trademark_data for field in required_fields):
            return False
        
        # Check if all required fields are lists (except trademark_text which is string)
        if not isinstance(trademark_data['reg_no'], list):
            return False
        if not isinstance(trademark_data['ser_no'], list):
            return False
        if not isinstance(trademark_data['trademark_text'], str):
            return False
        if not isinstance(trademark_data['int_cls_list'], list):
            return False
        if not isinstance(trademark_data['full_filename'], list):
            return False
        
        # Check if filename (without .webp) matches one of the ser_no values
        filename_base = filename.replace('.webp', '')
        if filename_base not in trademark_data['ser_no']:
            return False
        
        full_filename_bases = [fname.replace('_full.webp', '') for fname in trademark_data['full_filename'] if fname.endswith('_full.webp')]
        # Check if all ser_no values have corresponding full filenames
        if not all(ser_no in full_filename_bases for ser_no in trademark_data['ser_no']):
            return False
        
        # Check if all full_filename entries follow the correct format
        if not all(fname.endswith('_full.webp') for fname in trademark_data['full_filename']):
            return False
    
    return True

pattern_result = df['images'].apply(check_trademark_pattern)

# Filter to only rows that have non-empty trademarks (exclude None values)
has_trademarks_mask = pattern_result.notna()
trademark_rows_df = df[has_trademarks_mask].copy()
trademark_pattern_results = pattern_result[has_trademarks_mask]

# Create two DataFrames - only for rows that have trademark data
follows_pattern_df = trademark_rows_df[trademark_pattern_results == True].copy()
doesnt_follow_pattern_df = trademark_rows_df[trademark_pattern_results == False].copy()

print(f"Total rows: {len(df)}")
print(f"Rows with trademark data: {len(trademark_rows_df)}")
print(f"Trademark rows that follow the pattern: {len(follows_pattern_df)}")
print(f"Trademark rows that don't follow the pattern: {len(doesnt_follow_pattern_df)}")

follows_pattern_df

doesnt_follow_pattern_df

import re

# Regex pattern for 8 digits followed by '.webp'
pattern = re.compile(r'^\d{8}\.webp$')

for index, row in doesnt_follow_pattern_df.iterrows():
    trademarks_data = row['images']['trademarks']
    id = row['id']
    
    for filename in trademarks_data.keys():
        if not pattern.match(filename):
            print(f"Case ID: {id}: Invalid filename → {filename}")

import re

# Regex pattern for 8 digits followed by '.webp'
pattern = re.compile(r'^\d{8}\.webp$')

for index, row in doesnt_follow_pattern_df.iterrows():
    trademarks_data = row['images']['trademarks']
    id = row['id']
    
    for filename in trademarks_data.keys():
        if not pattern.match(filename):
            print(f"Case ID: {id}: Invalid filename → {filename}")

cases_df[cases_df['id']==14745]

data = {'trademarks': {'78522083.webp': {'reg_no': ['3216354'], 'int_cls_list': [28], 'trademark_text': 'MADE IN U.S.A. BY THE U.S. PLAYING CARDCO. KEM PLAYING CARDS A A', 'full_filename': ['78522083_full.webp'], 'ser_no': ['78522083']}, '74192366.webp': {'reg_no': ['1709997'], 'int_cls_list': [16], 'trademark_text': 'None', 'full_filename': ['74192366_full.webp'], 'ser_no': ['74192366']}, '74022062.webp': {'reg_no': ['1644376'], 'int_cls_list': [16], 'trademark_text': 'None', 'full_filename': ['74022062_full.webp'], 'ser_no': ['74022062']}, '85536747.webp': {'reg_no': [''], 'int_cls_list': [28], 'trademark_text': 'None', 'full_filename': ['85536747_full.webp'], 'ser_no': ['85536747']}, '72333564.webp': {'reg_no': ['0920593'], 'int_cls_list': [16, 22], 'trademark_text': 'HERACLIO FOURNIER VARIOS PREMIOS VITORIA', 'full_filename': ['72333564_full.webp'], 'ser_no': ['72333564']}, '73615845.webp': {'reg_no': [''], 'int_cls_list': [16], 'trademark_text': 'None', 'full_filename': ['73615845_full.webp'], 'ser_no': ['73615845']}, '76261207.webp': {'reg_no': ['2694791'], 'int_cls_list': [16], 'trademark_text': 'BEE STINGER', 'full_filename': ['76261207_full.webp'], 'ser_no': ['76261207']}, '88218965.webp': {'reg_no': ['5775642', ''], 'int_cls_list': [28, 16, 22], 'trademark_text': 'MAVERICK', 'full_filename': ['88218965_full.webp', '72192088_full.webp'], 'ser_no': ['88218965', '72192088']}, '74192367.webp': {'reg_no': ['1686416'], 'int_cls_list': [16], 'trademark_text': 'A STREAMLINE PLAYING CARDS', 'full_filename': ['74192367_full.webp'], 'ser_no': ['74192367']}, '74254386.webp': {'reg_no': ['1771384'], 'int_cls_list': [16], 'trademark_text': '"BEE" NO.92 CLUB SPECIAL PLAYING CARDS MADE IN THE USA', 'full_filename': ['74254386_full.webp'], 'ser_no': ['74254386']}, '74055281.webp': {'reg_no': ['1634808', '6853223', '2399763', '', '3085786', '7646629', '7602729', '7185210'], 'int_cls_list': [16, 9, 22, 28, 25, 35, 42], 'trademark_text': 'BICYCLE', 'full_filename': ['74055281_full.webp', '90301008_full.webp', '75777108_full.webp', '71001803_full.webp', '76422557_full.webp', '79385091_full.webp', '79393465_full.webp', '79348463_full.webp'], 'ser_no': ['74055281', '90301008', '75777108', '71001803', '76422557', '79385091', '79393465', '79348463']}, '90560629.webp': {'reg_no': ['6777691'], 'int_cls_list': [28], 'trademark_text': 'MAVERICK PLAYING CARDS', 'full_filename': ['90560629_full.webp'], 'ser_no': ['90560629']}, '85869716.webp': {'reg_no': [''], 'int_cls_list': [25, 28], 'trademark_text': 'None', 'full_filename': ['85869716_full.webp'], 'ser_no': ['85869716']}, '72063556.webp': {'reg_no': [''], 'int_cls_list': [16, 22], 'trademark_text': 'None', 'full_filename': ['72063556_full.webp'], 'ser_no': ['72063556']}, '71617777.webp': {'reg_no': [''], 'int_cls_list': [16, 22], 'trademark_text': 'None', 'full_filename': ['71617777_full.webp'], 'ser_no': ['71617777']}, '77651624.webp': {'reg_no': ['4338944'], 'int_cls_list': [25], 'trademark_text': 'BEE 92', 'full_filename': ['77651624_dead_full.webp'], 'ser_no': ['77651624']}, '85869679.webp': {'reg_no': ['', '5786468', '7646630'], 'int_cls_list': [25, 28], 'trademark_text': 'US', 'full_filename': ['85869679_full.webp', '88179729_full.webp', '79385092_full.webp', '85793813_full.webp'], 'ser_no': ['85869679', '88179729', '79385092', '85793813']}, '73454359.webp': {'reg_no': ['1301613'], 'int_cls_list': [16], 'trademark_text': 'A', 'full_filename': ['73454359_dead_full.webp'], 'ser_no': ['73454359']}, '71531483.webp': {'reg_no': [''], 'int_cls_list': [16, 22], 'trademark_text': 'KEM', 'full_filename': ['71531483_full.webp', '71531484_full.webp'], 'ser_no': ['71531483', '71531484']}, '73102235.webp': {'reg_no': [''], 'int_cls_list': [16], 'trademark_text': 'LOVISION', 'full_filename': ['73102235_full.webp'], 'ser_no': ['73102235']}, '85152491.webp': {'reg_no': ['3981035'], 'int_cls_list': [9], 'trademark_text': 'HOYLE THE OFFICIAL NAME IN GAMING', 'full_filename': ['85152491_full.webp'], 'ser_no': ['85152491']}, '74356761.webp': {'reg_no': ['1799328', '1794974', '2893563', ''], 'int_cls_list': [16, 28, 22], 'trademark_text': 'AVIATOR', 'full_filename': ['74356761_full.webp', '74356900_full.webp', '78317514_full.webp', '71277226_full.webp'], 'ser_no': ['74356761', '74356900', '78317514', '71277226']}, '85655615.webp': {'reg_no': [''], 'int_cls_list': [9, 28, 22], 'trademark_text': '"BEE"', 'full_filename': ['85655615_full.webp', '71004314_full.webp'], 'ser_no': ['85655615', '71004314']}, '71617778.webp': {'reg_no': [''], 'int_cls_list': [16, 22], 'trademark_text': '48', 'full_filename': ['71617778_full.webp'], 'ser_no': ['71617778']}, '71599353.webp': {'reg_no': [''], 'int_cls_list': [16, 22], 'trademark_text': 'TORPEDO', 'full_filename': ['71599353_full.webp'], 'ser_no': ['71599353']}, '88733633.webp': {'reg_no': ['6241311'], 'int_cls_list': [28], 'trademark_text': 'THIN CRUSHED', 'full_filename': ['88733633_full.webp'], 'ser_no': ['88733633']}, '73265258.webp': {'reg_no': [''], 'int_cls_list': [16], 'trademark_text': 'STREAMLINE', 'full_filename': ['73265258_full.webp'], 'ser_no': ['73265258']}, '87077057.webp': {'reg_no': ['5297624'], 'int_cls_list': [28], 'trademark_text': 'SHARKS ARE WILD', 'full_filename': ['87077057_dead_full.webp'], 'ser_no': ['87077057']}, '78841217.webp': {'reg_no': ['3428495'], 'int_cls_list': [28], 'trademark_text': 'POKER PEEK', 'full_filename': ['78841217_full.webp'], 'ser_no': ['78841217']}, '72243929.webp': {'reg_no': [''], 'int_cls_list': [16, 22], 'trademark_text': 'RIDER', 'full_filename': ['72243929_full.webp'], 'ser_no': ['72243929']}, '85625330.webp': {'reg_no': ['4584951'], 'int_cls_list': [9], 'trademark_text': 'PR-X', 'full_filename': ['85625330_dead_full.webp'], 'ser_no': ['85625330']}, '71414417.webp': {'reg_no': [''], 'int_cls_list': [22, 28], 'trademark_text': 'PO-KE-NO', 'full_filename': ['71414417_full.webp'], 'ser_no': ['71414417']}, '85163361.webp': {'reg_no': [''], 'int_cls_list': [28], 'trademark_text': 'MANDOLIN BACK', 'full_filename': ['85163361_full.webp'], 'ser_no': ['85163361']}, '85536702.webp': {'reg_no': [''], 'int_cls_list': [28], 'trademark_text': 'MAIDEN BACK', 'full_filename': ['85536702_full.webp'], 'ser_no': ['85536702']}, '72130845.webp': {'reg_no': [''], 'int_cls_list': [16, 22], 'trademark_text': 'NO. 77', 'full_filename': ['72130845_full.webp'], 'ser_no': ['72130845']}, '87442880.webp': {'reg_no': ['5638767'], 'int_cls_list': [28], 'trademark_text': 'NAILED IT', 'full_filename': ['87442880_full.webp'], 'ser_no': ['87442880']}, '86679025.webp': {'reg_no': ['5586544'], 'int_cls_list': [28], 'trademark_text': 'METALLUXE', 'full_filename': ['86679025_full.webp'], 'ser_no': ['86679025']}, '86254019.webp': {'reg_no': [''], 'int_cls_list': [28], 'trademark_text': 'FOURNIER', 'full_filename': ['86254019_full.webp'], 'ser_no': ['86254019']}, '78339224.webp': {'reg_no': ['2984139'], 'int_cls_list': [28], 'trademark_text': 'DON MANOLO', 'full_filename': ['78339224_full.webp'], 'ser_no': ['78339224']}, '72355006.webp': {'reg_no': ['0899141'], 'int_cls_list': [16, 22], 'trademark_text': 'DIAMOND', 'full_filename': ['72355006_full.webp'], 'ser_no': ['72355006']}, '72162041.webp': {'reg_no': [''], 'int_cls_list': [16, 22], 'trademark_text': 'CONSOLIDATED-DOUGHERTY', 'full_filename': ['72162041_full.webp'], 'ser_no': ['72162041']}, '73280574.webp': {'reg_no': [''], 'int_cls_list': [16], 'trademark_text': 'E-Z-SEE', 'full_filename': ['73280574_full.webp'], 'ser_no': ['73280574']}, '78841047.webp': {'reg_no': ['3413184'], 'int_cls_list': [28], 'trademark_text': 'CUPID BACK', 'full_filename': ['78841047_full.webp'], 'ser_no': ['78841047']}, '87100434.webp': {'reg_no': ['5184941'], 'int_cls_list': [28], 'trademark_text': 'CARAVAN', 'full_filename': ['87100434_dead_full.webp'], 'ser_no': ['87100434']}, '85655650.webp': {'reg_no': ['', '7171690'], 'int_cls_list': [9], 'trademark_text': 'BEE-TEK', 'full_filename': ['85655650_full.webp', '79329869_full.webp'], 'ser_no': ['85655650', '79329869']}, '73696378.webp': {'reg_no': ['', '1793627'], 'int_cls_list': [28, 16], 'trademark_text': 'ACCORDING TO HOYLE', 'full_filename': ['73696378_full.webp', '74282880_full.webp'], 'ser_no': ['73696378', '74282880']}, '71282952.webp': {'reg_no': [''], 'int_cls_list': [16, 22], 'trademark_text': 'CEL-U-TONE', 'full_filename': ['71282952_full.webp'], 'ser_no': ['71282952']}, '71324808.webp': {'reg_no': ['0295528'], 'int_cls_list': [16, 22], 'trademark_text': 'A. DOUGHERTY', 'full_filename': ['71324808_full.webp'], 'ser_no': ['71324808']}, '77365747.webp': {'reg_no': ['3484246'], 'int_cls_list': [28], 'trademark_text': 'BICYCLE PRO', 'full_filename': ['77365747_full.webp'], 'ser_no': ['77365747']}, '76335750.webp': {'reg_no': ['2607530'], 'int_cls_list': [16], 'trademark_text': 'BICYCLE RUMMY', 'full_filename': ['76335750_full.webp'], 'ser_no': ['76335750']}, '76062589.webp': {'reg_no': ['2665170'], 'int_cls_list': [16], 'trademark_text': '999 PLAYING CARDS STEAMBOAT 999 STEAMBOAT 999 STEAMBOAT BRAND OF THE U.S. PLAYING CARDS STEAMBOAT POKER STEAMBOAT POKER 999 THE U.S. PLAYING CARD CO. MADE IN U.S.A.', 'full_filename': ['76062589_full.webp'], 'ser_no': ['76062589']}, '73294803.webp': {'reg_no': ['1233517'], 'int_cls_list': [16], 'trademark_text': 'CLUB CASINO', 'full_filename': ['73294803_dead_full.webp'], 'ser_no': ['73294803']}, '76084204.webp': {'reg_no': ['2655511'], 'int_cls_list': [16], 'trademark_text': 'A DOUGHERTY REG U S PAT OFF AND TM OFF LINOID FINISH TALLY-HO POKER PLAYING CARDS NO 9 MADE IN USA THE U S PLAYING CARD CO', 'full_filename': ['76084204_full.webp'], 'ser_no': ['76084204']}, '75054159.webp': {'reg_no': ['2050539'], 'int_cls_list': [16], 'trademark_text': 'HOYLE OFFICIAL PLAYING CARDS POKER HOYLE PLASTIC COATED', 'full_filename': ['75054159_full.webp'], 'ser_no': ['75054159']}, '78343346.webp': {'reg_no': ['3149457'], 'int_cls_list': [28], 'trademark_text': 'BARAJA ESPANOLA SPANISH PLAYING CARDS 40 CARTAS DON MANOLO 40 CARDS VARIOS JUEGOS', 'full_filename': ['78343346_full.webp'], 'ser_no': ['78343346']}, '76062592.webp': {'reg_no': ['2655480'], 'int_cls_list': [16], 'trademark_text': 'POKER BICYCLE RIDER BACK PLAYING CARDS AIR CUSHION FINISH MADE IN U.S.A. THE U.S. PLAYING CARD CO. POKER 808', 'full_filename': ['76062592_full.webp'], 'ser_no': ['76062592']}, '76062590.webp': {'reg_no': ['2600170'], 'int_cls_list': [16], 'trademark_text': '1002 ALADDIN PLAYING CARDS GILDED EDGESTHE NATIONAL CARD CO.  THE U.S. PLAYING CARD CO. MADE IN U.S.A. 1002 ALADDIN.', 'full_filename': ['76062590_dead_full.webp'], 'ser_no': ['76062590']}, '60585116.webp': {'reg_no': ['585116'], 'int_cls_list': [], 'trademark_text': 'None', 'full_filename': ['60585116_dead_full.webp'], 'ser_no': ['60585116']}, '71072847.webp': {'reg_no': ['0094671'], 'int_cls_list': [16, 22], 'trademark_text': 'ARISTOCRAT', 'full_filename': ['71072847_full.webp'], 'ser_no': ['71072847']}, '71017757.webp': {'reg_no': ['0055524'], 'int_cls_list': [16, 22, 28], 'trademark_text': 'None', 'full_filename': ['71017757_full.webp'], 'ser_no': ['71017757']}, '71015707.webp': {'reg_no': ['0055100'], 'int_cls_list': [16, 22], 'trademark_text': 'None', 'full_filename': ['71015707_full.webp'], 'ser_no': ['71015707']}, '71124456.webp': {'reg_no': [''], 'int_cls_list': [16, 22], 'trademark_text': 'THE NATIONAL CARD CO.', 'full_filename': ['71124456_full.webp'], 'ser_no': ['71124456']}, '98454593.webp': {'reg_no': ['7735703'], 'int_cls_list': [28], 'trademark_text': 'THE U.S. PLAYING CARD COMPANY', 'full_filename': ['98454593_full.webp'], 'ser_no': ['98454593']}, '71151122.webp': {'reg_no': ['0155547'], 'int_cls_list': [16, 22], 'trademark_text': 'None', 'full_filename': ['71151122_full.webp'], 'ser_no': ['71151122']}, '71025427.webp': {'reg_no': ['0062565'], 'int_cls_list': [22, 28], 'trademark_text': 'AIR-CUSHION FINISH', 'full_filename': ['71025427_full.webp'], 'ser_no': ['71025427']}, '71300290.webp': {'reg_no': ['0275340'], 'int_cls_list': [16, 22], 'trademark_text': 'CLUB SPECIAL', 'full_filename': ['71300290_full.webp'], 'ser_no': ['71300290']}, '71001582.webp': {'reg_no': ['0048874'], 'int_cls_list': [16, 22], 'trademark_text': 'RAMBLER', 'full_filename': ['71001582_full.webp'], 'ser_no': ['71001582']}, '74022068.webp': {'reg_no': ['1645186'], 'int_cls_list': [16], 'trademark_text': 'None', 'full_filename': ['74022068_full.webp'], 'ser_no': ['74022068']}, '71009223.webp': {'reg_no': ['0052014'], 'int_cls_list': [16, 22], 'trademark_text': 'SQUEEZER TRIP', 'full_filename': ['71009223_full.webp'], 'ser_no': ['71009223']}, '85437799.webp': {'reg_no': [''], 'int_cls_list': [28], 'trademark_text': 'None', 'full_filename': ['85437799_full.webp'], 'ser_no': ['85437799']}, '71132956.webp': {'reg_no': ['0143499'], 'int_cls_list': [22, 28], 'trademark_text': 'None', 'full_filename': ['71132956_full.webp'], 'ser_no': ['71132956']}, '72130844.webp': {'reg_no': [''], 'int_cls_list': [16, 22], 'trademark_text': 'None', 'full_filename': ['72130844_full.webp'], 'ser_no': ['72130844']}, '71130767.webp': {'reg_no': ['0138079'], 'int_cls_list': [16, 22], 'trademark_text': 'CAMBRIC', 'full_filename': ['71130767_full.webp'], 'ser_no': ['71130767']}, '71156614.webp': {'reg_no': ['0153634'], 'int_cls_list': [16, 22], 'trademark_text': 'None', 'full_filename': ['71156614_full.webp'], 'ser_no': ['71156614']}, '71212978.webp': {'reg_no': ['0204255'], 'int_cls_list': [16, 22], 'trademark_text': 'None', 'full_filename': ['71212978_full.webp'], 'ser_no': ['71212978']}, '71172176.webp': {'reg_no': ['167072'], 'int_cls_list': [16, 22], 'trademark_text': 'BATTLE AXE', 'full_filename': ['71172176_dead_full.webp'], 'ser_no': ['71172176']}, '75099216.webp': {'reg_no': ['2049103'], 'int_cls_list': [16], 'trademark_text': 'A', 'full_filename': ['75099216_full.webp'], 'ser_no': ['75099216']}, '78702350.webp': {'reg_no': ['3123721'], 'int_cls_list': [28], 'trademark_text': 'None', 'full_filename': ['78702350_full.webp'], 'ser_no': ['78702350']}, '71001568.webp': {'reg_no': ['0049420'], 'int_cls_list': [22, 28], 'trademark_text': 'CONGRESS', 'full_filename': ['71001568_full.webp'], 'ser_no': ['71001568']}, '71010296.webp': {'reg_no': ['0051202'], 'int_cls_list': [22, 28], 'trademark_text': 'None', 'full_filename': ['71010296_full.webp'], 'ser_no': ['71010296']}, '70023782.webp': {'reg_no': ['0023782'], 'int_cls_list': [16, 22], 'trademark_text': 'None', 'full_filename': ['70023782_full.webp'], 'ser_no': ['70023782']}, '71150168.webp': {'reg_no': ['0151272'], 'int_cls_list': [16, 22], 'trademark_text': '808', 'full_filename': ['71150168_full.webp'], 'ser_no': ['71150168']}, '71023308.webp': {'reg_no': ['0060313'], 'int_cls_list': [16, 22], 'trademark_text': 'None', 'full_filename': ['71023308_full.webp'], 'ser_no': ['71023308']}, '71628137.webp': {'reg_no': ['566741'], 'int_cls_list': [3, 4], 'trademark_text': 'BEAUTIFLOR', 'full_filename': ['71628137_dead_full.webp'], 'ser_no': ['71628137']}, '74360043.webp': {'reg_no': ['1799338'], 'int_cls_list': [16], 'trademark_text': 'FRESH LOOK', 'full_filename': ['74360043_dead_full.webp'], 'ser_no': ['74360043']}, '77114975.webp': {'reg_no': ['3427752'], 'int_cls_list': [28], 'trademark_text': 'None', 'full_filename': ['77114975_full.webp'], 'ser_no': ['77114975']}, '78543028.webp': {'reg_no': ['3532410'], 'int_cls_list': [25], 'trademark_text': 'None', 'full_filename': ['78543028_full.webp'], 'ser_no': ['78543028']}, '79329868.webp': {'reg_no': ['7171689'], 'int_cls_list': [9], 'trademark_text': 'CLICK-SHOE', 'full_filename': ['79329868_full.webp'], 'ser_no': ['79329868']}, '79385089.webp': {'reg_no': ['7646627', '7601967'], 'int_cls_list': [25, 9, 16, 28, 41, 42], 'trademark_text': 'None', 'full_filename': ['79385089_full.webp', '79370370_full.webp'], 'ser_no': ['79385089', '79370370']}, '97936108.webp': {'reg_no': ['7771058'], 'int_cls_list': [28, 35, 41], 'trademark_text': 'CARDTOPIA', 'full_filename': ['97936108_full.webp'], 'ser_no': ['97936108']}, '97213147.webp': {'reg_no': ['7082129'], 'int_cls_list': [28], 'trademark_text': 'PO KE NO', 'full_filename': ['97213147_full.webp'], 'ser_no': ['97213147']}, '.webp': {'reg_no': ['7626320'], 'int_cls_list': [28], 'trademark_text': 'None', 'full_filename': ['98490116_cert.webp'], 'ser_no': ['']}, '78522048.webp': {'reg_no': ['3819964'], 'int_cls_list': [28], 'trademark_text': 'None', 'full_filename': ['78522048_full.webp'], 'ser_no': ['78522048']}, '77967704.webp': {'reg_no': ['4069060'], 'int_cls_list': [28], 'trademark_text': 'None', 'full_filename': ['77967704_full.webp'], 'ser_no': ['77967704']}, '79385090.webp': {'reg_no': ['7646628'], 'int_cls_list': [25], 'trademark_text': 'None', 'full_filename': ['79385090_full.webp'], 'ser_no': ['79385090']}, '79371713.webp': {'reg_no': ['7628645', '7602730'], 'int_cls_list': [41, 35], 'trademark_text': 'BICYCLE TRUSTED SINCE 1885', 'full_filename': ['79371713_full.webp', '79393466_full.webp'], 'ser_no': ['79371713', '79393466']}, '98093639.webp': {'reg_no': ['7327532'], 'int_cls_list': [28], 'trademark_text': 'CASINO QUALITY CLUB SPECIAL "BEE" PLAYING CARDS', 'full_filename': ['98093639_full.webp'], 'ser_no': ['98093639']}, '97348789.webp': {'reg_no': ['7059845'], 'int_cls_list': [28], 'trademark_text': 'BICYCLE TRUSTED SINCE 1885 PLAYING CARDS STANDARD', 'full_filename': ['97348789_full.webp'], 'ser_no': ['97348789']}, '78238987.webp': {'reg_no': ['2862722'], 'int_cls_list': [28], 'trademark_text': 'None', 'full_filename': ['78238987_full.webp'], 'ser_no': ['78238987']}, '71156613.webp': {'reg_no': ['0153633'], 'int_cls_list': [16, 22], 'trademark_text': 'None', 'full_filename': ['71156613_full.webp'], 'ser_no': ['71156613']}, '71003953.webp': {'reg_no': ['0049103'], 'int_cls_list': [22, 28], 'trademark_text': 'SQUEEZERS', 'full_filename': ['71003953_full.webp'], 'ser_no': ['71003953']}, '71599351.webp': {'reg_no': ['554651'], 'int_cls_list': [16, 22], 'trademark_text': 'MOHAWK', 'full_filename': ['71599351_dead_full.webp'], 'ser_no': ['71599351']}, '71151120.webp': {'reg_no': ['153892'], 'int_cls_list': [16, 22], 'trademark_text': 'None', 'full_filename': ['71151120_dead_full.webp'], 'ser_no': ['71151120']}, '71151121.webp': {'reg_no': ['153893'], 'int_cls_list': [16, 22], 'trademark_text': 'None', 'full_filename': ['71151121_dead_full.webp'], 'ser_no': ['71151121']}, '73480481.webp': {'reg_no': ['', '1686549', '1901704'], 'int_cls_list': [14, 16, 28, 24], 'trademark_text': 'HOYLE', 'full_filename': ['73480481_full.webp', '73419402_full.webp', '74108438_full.webp', '73112048_full.webp', '74479589_full.webp'], 'ser_no': ['73480481', '73419402', '74108438', '73112048', '74479589']}, 'IN_DC_1_25-cv-08313_2025-07-22_12_ 0_Exhibit_1_page112_0.webp': {'reg_no': ['4338944'], 'int_cls_list': [25], 'trademark_text': ['BEE 92'], 'full_filename': ['IN_DC_1_25-cv-08313_2025-07-22_12_ 0_Exhibit_1_page112_0_full.webp']}, 'IN_DC_1_25-cv-08313_2025-07-22_12_ 0_Exhibit_1_page132_0.webp': {'reg_no': ['1799328'], 'int_cls_list': [16], 'trademark_text': ['AVIATOR'], 'full_filename': ['IN_DC_1_25-cv-08313_2025-07-22_12_ 0_Exhibit_1_page132_0_full.webp']}, 'IN_DC_1_25-cv-08313_2025-07-22_12_ 0_Exhibit_1_page196_0.webp': {'reg_no': ['2607530'], 'int_cls_list': [16], 'trademark_text': ['BICYCLE RUMMY'], 'full_filename': ['IN_DC_1_25-cv-08313_2025-07-22_12_ 0_Exhibit_1_page196_0_full.webp']}, '1_25-cv-08313_regno_4338944.webp': {'reg_no': ['4338944'], 'int_cls_list': [25], 'trademark_text': ['BEE 92'], 'full_filename': ['1_25-cv-08313_regno_4338944_full.webp']}, '1_25-cv-08313_regno_0048877.webp': {'reg_no': ['0048877'], 'int_cls_list': [16, 22], 'trademark_text': ['ALADDIN'], 'full_filename': ['1_25-cv-08313_regno_0048877_full.webp']}, '1_25-cv-08313_regno_0179178.webp': {'reg_no': ['0179178'], 'int_cls_list': [29, 30], 'trademark_text': ['FARMHOUSE'], 'full_filename': ['1_25-cv-08313_regno_0179178_full.webp']}, '79329868_tm.webp': {'reg_no': ['7171689'], 'int_cls_list': [9], 'trademark_text': ['CLICK-SHOE'], 'full_filename': ['79329868_cert.webp']}, '79329869_tm.webp': {'reg_no': ['7171690'], 'int_cls_list': [9], 'trademark_text': ['BEE-TEK'], 'full_filename': ['79329869_cert.webp']}, '85793813_tm.webp': {'reg_no': ['4634386'], 'int_cls_list': [28], 'trademark_text': ['US'], 'full_filename': ['85793813_cert.webp']}, '88733633_tm.webp': {'reg_no': ['6241311'], 'int_cls_list': [28], 'trademark_text': ['THIN CRUSHED'], 'full_filename': ['88733633_cert.webp']}, '98093639_tm.webp': {'reg_no': ['7327532'], 'int_cls_list': [28], 'trademark_text': ['CASINO QUALITY CLUB SPECIAL "BEE" PLAYING CARDS'], 'full_filename': ['98093639_cert.webp']}, '86679025_tm.webp': {'reg_no': ['5586544'], 'int_cls_list': [28], 'trademark_text': ['METALLUXE'], 'full_filename': ['86679025_cert.webp']}, '85163361_tm.webp': {'reg_no': ['4058738'], 'int_cls_list': [28], 'trademark_text': ['MANDOLIN BACK'], 'full_filename': ['85163361_cert.webp']}, '77114975_tm.webp': {'reg_no': ['3427752'], 'int_cls_list': [28], 'trademark_text': [''], 'full_filename': ['77114975_cert.webp']}, '78841217_tm.webp': {'reg_no': ['3428495'], 'int_cls_list': [28], 'trademark_text': ['POKER PEEK'], 'full_filename': ['78841217_cert.webp']}, '85437799_tm.webp': {'reg_no': ['4160512'], 'int_cls_list': [28], 'trademark_text': [''], 'full_filename': ['85437799_cert.webp']}, '78343346_tm.webp': {'reg_no': ['3149457'], 'int_cls_list': [28], 'trademark_text': ['BARAJA ESPANOLA SPANISH PLAYING CARDS 40 CARTAS DON MANOLO 40 CARDS VARIOS JUEGOS'], 'full_filename': ['78343346_cert.webp']}, '90560629_tm.webp': {'reg_no': ['6777691'], 'int_cls_list': [28], 'trademark_text': ['MAVERICK PLAYING CARDS'], 'full_filename': ['90560629_cert.webp']}, '88218965_tm.webp': {'reg_no': ['5775642'], 'int_cls_list': [28], 'trademark_text': ['MAVERICK'], 'full_filename': ['88218965_cert.webp']}, '79385091_tm.webp': {'reg_no': ['7646629'], 'int_cls_list': [25], 'trademark_text': ['BICYCLE'], 'full_filename': ['79385091_cert.webp']}, '76422557_tm.webp': {'reg_no': ['3085786'], 'int_cls_list': [9], 'trademark_text': ['BICYCLE'], 'full_filename': ['76422557_cert.webp']}, '78543028_tm.webp': {'reg_no': ['3532410'], 'int_cls_list': [25], 'trademark_text': [''], 'full_filename': ['78543028_cert.webp']}, '79385089_tm.webp': {'reg_no': ['7646627'], 'int_cls_list': [25], 'trademark_text': [''], 'full_filename': ['79385089_cert.webp']}, '97348789_tm.webp': {'reg_no': ['7059845'], 'int_cls_list': [28], 'trademark_text': ['BICYCLE TRUSTED SINCE 1885 PLAYING CARDS STANDARD'], 'full_filename': ['97348789_cert.webp']}, '78238987_tm.webp': {'reg_no': ['2862722'], 'int_cls_list': [28], 'trademark_text': [''], 'full_filename': ['78238987_cert.webp']}, '85655615_tm.webp': {'reg_no': ['4397415'], 'int_cls_list': [9, 28], 'trademark_text': ['"BEE"'], 'full_filename': ['85655615_cert.webp']}, '85869716_tm.webp': {'reg_no': ['4716967'], 'int_cls_list': [25, 28], 'trademark_text': [''], 'full_filename': ['85869716_cert.webp']}, '79393466_tm.webp': {'reg_no': ['7602730'], 'int_cls_list': [35], 'trademark_text': ['BICYCLE TRUSTED SINCE 1885'], 'full_filename': ['79393466_cert.webp']}, '98454593_tm.webp': {'reg_no': ['7735703'], 'int_cls_list': [28], 'trademark_text': ['THE U.S. PLAYING CARD COMPANY'], 'full_filename': ['98454593_cert.webp']}, '78522048_tm.webp': {'reg_no': ['3819964'], 'int_cls_list': [28], 'trademark_text': [''], 'full_filename': ['78522048_cert.webp']}, '78339224_tm.webp': {'reg_no': ['2984139'], 'int_cls_list': [28], 'trademark_text': ['DON MANOLO'], 'full_filename': ['78339224_cert.webp']}, '78841047_tm.webp': {'reg_no': ['3413184'], 'int_cls_list': [28], 'trademark_text': ['CUPID BACK'], 'full_filename': ['78841047_cert.webp']}, '90301008_tm.webp': {'reg_no': ['6853223'], 'int_cls_list': [9], 'trademark_text': ['BICYCLE'], 'full_filename': ['90301008_cert.webp']}, '78522083_tm.webp': {'reg_no': ['3216354'], 'int_cls_list': [28], 'trademark_text': ['MADE IN U.S.A. BY THE U.S. PLAYING CARDCO. KEM PLAYING CARDS A A'], 'full_filename': ['78522083_cert.webp']}, '77365747_tm.webp': {'reg_no': ['3484246'], 'int_cls_list': [28], 'trademark_text': ['BICYCLE PRO'], 'full_filename': ['77365747_cert.webp']}, '85152491_tm.webp': {'reg_no': ['3981035'], 'int_cls_list': [9], 'trademark_text': ['HOYLE THE OFFICIAL NAME IN GAMING'], 'full_filename': ['85152491_cert.webp']}, '88179729_tm.webp': {'reg_no': ['5786468'], 'int_cls_list': [28], 'trademark_text': ['US'], 'full_filename': ['88179729_cert.webp']}, '79371713_tm.webp': {'reg_no': ['7628645'], 'int_cls_list': [41], 'trademark_text': ['BICYCLE TRUSTED SINCE 1885'], 'full_filename': ['79371713_cert.webp']}, '71124456_tm.webp': {'reg_no': ['0133071'], 'int_cls_list': [16, 22], 'trademark_text': ['THE NATIONAL CARD CO.'], 'full_filename': ['71124456_cert.webp']}, '78317514_tm.webp': {'reg_no': ['2893563'], 'int_cls_list': [28], 'trademark_text': ['AVIATOR'], 'full_filename': ['78317514_cert.webp']}, '77967704_tm.webp': {'reg_no': ['4069060'], 'int_cls_list': [28], 'trademark_text': [''], 'full_filename': ['77967704_cert.webp']}, '79370370_tm.webp': {'reg_no': ['7601967'], 'int_cls_list': [9, 16, 28, 41, 42], 'trademark_text': [''], 'full_filename': ['79370370_cert.webp']}, '79385090_tm.webp': {'reg_no': ['7646628'], 'int_cls_list': [25], 'trademark_text': [''], 'full_filename': ['79385090_cert.webp']}, '97213147_tm.webp': {'reg_no': ['7082129'], 'int_cls_list': [28], 'trademark_text': ['PO KE NO'], 'full_filename': ['97213147_cert.webp']}, '98490114_tm.webp': {'reg_no': ['7626319'], 'int_cls_list': [28], 'trademark_text': ['PRESTIGE'], 'full_filename': ['98490114_cert.webp']}, '98490116_tm.webp': {'reg_no': ['7626320'], 'int_cls_list': [28], 'trademark_text': ['DURA-FLEX'], 'full_filename': ['98490116_cert.webp']}, '97936108_tm.webp': {'reg_no': ['7771058'], 'int_cls_list': [28, 35, 41], 'trademark_text': ['CARDTOPIA'], 'full_filename': ['97936108_cert.webp']}, '85655650_tm.webp': {'reg_no': ['4585005'], 'int_cls_list': [9], 'trademark_text': ['BEE-TEK'], 'full_filename': ['85655650_cert.webp']}, '85536747_tm.webp': {'reg_no': ['4351088'], 'int_cls_list': [28], 'trademark_text': [''], 'full_filename': ['85536747_cert.webp']}, '85869679_tm.webp': {'reg_no': ['4716966'], 'int_cls_list': [25, 28], 'trademark_text': ['US'], 'full_filename': ['85869679_cert.webp']}, '79393465_tm.webp': {'reg_no': ['7602729'], 'int_cls_list': [35], 'trademark_text': ['BICYCLE'], 'full_filename': ['79393465_cert.webp']}, '85536702_tm.webp': {'reg_no': ['4251560'], 'int_cls_list': [28], 'trademark_text': ['MAIDEN BACK'], 'full_filename': ['85536702_cert.webp']}, '79385092_tm.webp': {'reg_no': ['7646630'], 'int_cls_list': [25], 'trademark_text': ['US'], 'full_filename': ['79385092_cert.webp']}, '79348463_tm.webp': {'reg_no': ['7185210'], 'int_cls_list': [42], 'trademark_text': ['BICYCLE'], 'full_filename': ['79348463_cert.webp']}, '74282880_tm.webp': {'reg_no': ['1793627'], 'int_cls_list': [16], 'trademark_text': ['ACCORDING TO HOYLE'], 'full_filename': ['74282880_cert.webp']}, '73480481_tm.webp': {'reg_no': ['1342484'], 'int_cls_list': [14, 16, 28], 'trademark_text': ['HOYLE'], 'full_filename': ['73480481_cert.webp']}, '71156613_tm.webp': {'reg_no': ['0153633'], 'int_cls_list': [16, 22], 'trademark_text': [''], 'full_filename': ['71156613_cert.webp']}, '73696378_tm.webp': {'reg_no': ['1495231'], 'int_cls_list': [28], 'trademark_text': ['ACCORDING TO HOYLE'], 'full_filename': ['73696378_cert.webp']}, '71531483_tm.webp': {'reg_no': ['0505214'], 'int_cls_list': [16, 22], 'trademark_text': ['KEM'], 'full_filename': ['71531483_cert.webp']}, '71414417_tm.webp': {'reg_no': ['0366741'], 'int_cls_list': [22, 28], 'trademark_text': ['PO-KE-NO'], 'full_filename': ['71414417_cert.webp']}, '71531484_tm.webp': {'reg_no': ['0505215'], 'int_cls_list': [16, 22], 'trademark_text': ['KEM'], 'full_filename': ['71531484_cert.webp']}, '71003953_tm.webp': {'reg_no': ['0049103'], 'int_cls_list': [22, 28], 'trademark_text': ['SQUEEZERS'], 'full_filename': ['71003953_cert.webp']}}, 'patents': {}, 'copyrights': {}}

from collections import defaultdict

# Group keys by their first 8 digits
prefix_groups = defaultdict(list)

for key in data['trademarks']:
    # Extract first 8 digits
    digits_only = ''.join(filter(str.isdigit, key))
    prefix = digits_only[:8] if len(digits_only) >= 8 else None
    if prefix:
        prefix_groups[prefix].append(key)

# Classify keys into duplicated and unique
duplicated_keys = []
unique_keys = []

for keys in prefix_groups.values():
    if len(keys) > 1:
        duplicated_keys.extend(keys)
    else:
        unique_keys.extend(keys)

# Output results
print("✅ Duplicated keys (based on first 8 digits):")
for key in duplicated_keys:
    print(f"  {key}")

print("\n❎ Unique keys (no other key shares the same first 8 digits):")
for key in unique_keys:
    print(f"  {key}")


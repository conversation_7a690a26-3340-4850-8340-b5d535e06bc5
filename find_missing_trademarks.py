import os
import pandas as pd
from DatabaseManagement.ImportExport import get_table_from_GZ
from IP.Trademarks_Bulk.trademark_db import get_db_connection, get_table_from_db
import json

def find_missing_trademarks():
    # Get cases from GZ database
    print("Fetching cases from GZ database...")
    df = get_table_from_GZ("tb_case", force_refresh=True)
    
    # Initialize counters
    missing_count = 0
    missing_trademarks = []
    
    print("Processing case data...")
    # Process each case
    for index, row in df.iterrows():
        if not row['images'] or not isinstance(row['images'], dict):
            continue
            
        # Get trademarks from images
        trademarks = row['images'].get('trademarks', {})
        if not trademarks:
            continue
            
        # Process each trademark
        for image_name, trademark_info in trademarks.items():
            if not isinstance(trademark_info, dict):
                continue
                
            # Get serial numbers
            ser_numbers = trademark_info.get('ser_no', [])
            if not isinstance(ser_numbers, list):
                ser_numbers = [ser_numbers]
            
            # Process each serial number
            for ser_no in ser_numbers:
                if not ser_no:  # Skip empty/null serial numbers
                    continue
                    
                # Clean up serial number
                ser_no = str(ser_no).strip()
                if not ser_no:
                    continue
                
                # Query PostgreSQL database for this serial number
                with get_db_connection() as conn:
                    with conn.cursor() as cur:
                        cur.execute("""
                            SELECT ser_no 
                            FROM trademarks 
                            WHERE ser_no = %s
                        """, (ser_no,))
                        result = cur.fetchone()
                        
                        if not result:  # Trademark not found in database
                            missing_count += 1
                            reg_numbers = trademark_info.get('reg_no', [])
                            if not isinstance(reg_numbers, list):
                                reg_numbers = [reg_numbers]
                            
                            missing_trademarks.append({
                                'plaintiff_id': row['plaintiff_id'],
                                'docket': row['docket'],
                                'ser_no': ser_no,
                                'reg_no': reg_numbers[0] if reg_numbers else None,
                                'trademark_text': trademark_info.get('trademark_text', '')
                            })

    print(f"\nTotal missing trademarks: {missing_count}")
    
    # Save missing trademarks to CSV
    if missing_trademarks:
        df_missing = pd.DataFrame(missing_trademarks)
        output_file = 'missing_trademarks.csv'
        df_missing.to_csv(output_file, index=False)
        print(f"Missing trademarks have been saved to {output_file}")
        print("\nSample of missing trademarks:")
        print(df_missing.head())

if __name__ == "__main__":
    find_missing_trademarks()

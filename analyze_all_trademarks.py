#!/usr/bin/env python3
"""
Analyze All Trademarks in tb_case

This script extracts and analyzes all trademarks from tb_case database
to understand the complete structure and data patterns.
"""

import pandas as pd
import json
from datetime import datetime
from DatabaseManagement.ImportExport import get_table_from_GZ

def analyze_all_trademarks():
    """Extract and analyze all trademarks from tb_case"""
    
    print("=== Trademark Analysis from tb_case ===")
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Get cases from GZ database
    print("Fetching cases from GZ database...")
    df = get_table_from_GZ("tb_case", force_refresh=True)
    print(f"Loaded {len(df)} cases")
    
    # Initialize counters and lists
    all_trademarks = []
    cases_with_trademarks = 0
    total_trademark_entries = 0
    processed_cases = 0
    
    print("Processing case data...")
    
    # Process each case
    for _, row in df.iterrows():
        processed_cases += 1
        
        # Progress reporting
        if processed_cases % 1000 == 0:
            print(f"Processed {processed_cases}/{len(df)} cases...")
        
        if not row['images'] or not isinstance(row['images'], dict):
            continue
            
        # Get trademarks from images
        trademarks = row['images'].get('trademarks', {})
        if not trademarks:
            continue
        
        if not isinstance(trademarks, dict):
            continue
        
        cases_with_trademarks += 1
            
        # Process each trademark
        for image_name, trademark_info in trademarks.items():
            total_trademark_entries += 1
            
            if not isinstance(trademark_info, dict):
                continue
            
            # Get serial numbers - handle both single values and arrays
            ser_numbers = trademark_info.get('ser_no', [])
            if not isinstance(ser_numbers, list):
                ser_numbers = [ser_numbers] if ser_numbers else []
            
            # Get registration numbers - handle both single values and arrays
            reg_numbers = trademark_info.get('reg_no', [])
            if not isinstance(reg_numbers, list):
                reg_numbers = [reg_numbers] if reg_numbers else []
            
            # Get full filenames
            full_filenames = trademark_info.get('full_filename', [])
            if not isinstance(full_filenames, list):
                full_filenames = [full_filenames] if full_filenames else []
            
            # Get int_cls_list
            int_cls_list = trademark_info.get('int_cls_list', [])
            if not isinstance(int_cls_list, list):
                int_cls_list = [int_cls_list] if int_cls_list else []
            
            # Create a record for each ser_no (since that's what we're tracking)
            if ser_numbers:
                for i, ser_no in enumerate(ser_numbers):
                    if ser_no:  # Skip empty ser_no
                        all_trademarks.append({
                            'case_id': row.get('id', 'Unknown'),
                            'plaintiff_id': row.get('plaintiff_id', 'Unknown'),
                            'docket': row.get('docket', 'Unknown'),
                            'image_filename': image_name,
                            'ser_no': str(ser_no).strip(),
                            'reg_no': reg_numbers[i] if i < len(reg_numbers) and reg_numbers[i] else 'None',
                            'all_reg_nos': ', '.join([str(r) for r in reg_numbers if r]) if reg_numbers else 'None',
                            'trademark_text': trademark_info.get('trademark_text', ''),
                            'int_cls_list': ', '.join([str(c) for c in int_cls_list]) if int_cls_list else 'None',
                            'full_filename': full_filenames[i] if i < len(full_filenames) and full_filenames[i] else 'None',
                            'all_full_filenames': ', '.join([str(f) for f in full_filenames if f]) if full_filenames else 'None',
                            'total_ser_nos': len(ser_numbers),
                            'total_reg_nos': len([r for r in reg_numbers if r]),
                            'total_full_filenames': len([f for f in full_filenames if f])
                        })
            else:
                # Handle cases where there's no ser_no but other data exists
                all_trademarks.append({
                    'case_id': row.get('id', 'Unknown'),
                    'plaintiff_id': row.get('plaintiff_id', 'Unknown'),
                    'docket': row.get('docket', 'Unknown'),
                    'image_filename': image_name,
                    'ser_no': 'None',
                    'reg_no': reg_numbers[0] if reg_numbers and reg_numbers[0] else 'None',
                    'all_reg_nos': ', '.join([str(r) for r in reg_numbers if r]) if reg_numbers else 'None',
                    'trademark_text': trademark_info.get('trademark_text', ''),
                    'int_cls_list': ', '.join([str(c) for c in int_cls_list]) if int_cls_list else 'None',
                    'full_filename': full_filenames[0] if full_filenames and full_filenames[0] else 'None',
                    'all_full_filenames': ', '.join([str(f) for f in full_filenames if f]) if full_filenames else 'None',
                    'total_ser_nos': 0,
                    'total_reg_nos': len([r for r in reg_numbers if r]),
                    'total_full_filenames': len([f for f in full_filenames if f])
                })

    # Print summary
    print(f"\n=== SUMMARY ===")
    print(f"Total cases processed: {processed_cases}")
    print(f"Cases with trademarks: {cases_with_trademarks}")
    print(f"Total trademark image entries: {total_trademark_entries}")
    print(f"Total trademark records (expanded by ser_no): {len(all_trademarks)}")
    
    # Save all trademarks to CSV
    if all_trademarks:
        df_trademarks = pd.DataFrame(all_trademarks)
        output_file = 'all_trademarks_from_tb_case.csv'
        df_trademarks.to_csv(output_file, index=False)
        print(f"\nAll trademarks saved to: {output_file}")
        
        print(f"\nSample of trademarks:")
        print(df_trademarks.head(10))
        
        # Additional statistics
        print(f"\nDetailed Statistics:")
        print(f"- Unique plaintiff_ids: {df_trademarks['plaintiff_id'].nunique()}")
        print(f"- Unique dockets: {df_trademarks['docket'].nunique()}")
        print(f"- Unique ser_nos: {df_trademarks[df_trademarks['ser_no'] != 'None']['ser_no'].nunique()}")
        print(f"- Records with no ser_no: {len(df_trademarks[df_trademarks['ser_no'] == 'None'])}")
        print(f"- Records with multiple ser_nos: {len(df_trademarks[df_trademarks['total_ser_nos'] > 1])}")
        print(f"- Records with multiple reg_nos: {len(df_trademarks[df_trademarks['total_reg_nos'] > 1])}")
        print(f"- Records with trademark text: {len(df_trademarks[df_trademarks['trademark_text'] != ''])}")
        
        # Show some examples of complex cases
        print(f"\nExamples of trademarks with multiple ser_nos:")
        multiple_ser = df_trademarks[df_trademarks['total_ser_nos'] > 1].head(3)
        for _, row in multiple_ser.iterrows():
            print(f"  - Image: {row['image_filename']}, ser_nos: {row['total_ser_nos']}, reg_nos: {row['total_reg_nos']}")
        
        print(f"\nExamples of trademarks with multiple reg_nos:")
        multiple_reg = df_trademarks[df_trademarks['total_reg_nos'] > 1].head(3)
        for _, row in multiple_reg.iterrows():
            print(f"  - Image: {row['image_filename']}, ser_nos: {row['total_ser_nos']}, reg_nos: {row['total_reg_nos']}")
            print(f"    All reg_nos: {row['all_reg_nos']}")
    else:
        print("No trademarks found!")
    
    print(f"\nCompleted at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    return all_trademarks

if __name__ == "__main__":
    analyze_all_trademarks()

#!/usr/bin/env python3
"""
Trademark Missing Check<PERSON>t

This script identifies trademarks that exist in tb_case but are missing from PostgreSQL database.
It also logs rows with different/unexpected data structures for analysis.
"""

import pandas as pd
import json
from datetime import datetime
from DatabaseManagement.ImportExport import get_table_from_GZ
from IP.Trademarks_Bulk.trademark_db import get_table_from_db

def log_structure_issue(issue_type, docket, data, log_file="structure_issues.log"):
    """Log rows with unexpected data structures"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    with open(log_file, "a", encoding="utf-8") as f:
        f.write(f"[{timestamp}] {issue_type} - Docket: {docket}\n")
        f.write(f"Data: {json.dumps(data, indent=2, default=str)}\n")
        f.write("-" * 80 + "\n")

def analyze_trademark_structure(trademark_info, docket, image_name):
    """Analyze and log trademark data structure variations"""
    issues = []
    
    # Check ser_no structure
    ser_no = trademark_info.get('ser_no')
    if ser_no is None:
        issues.append("Missing ser_no field")
    elif isinstance(ser_no, list):
        if len(ser_no) == 0:
            issues.append("Empty ser_no list")
        elif len(ser_no) > 1:
            issues.append(f"Multiple ser_no values: {ser_no}")
    elif not isinstance(ser_no, (str, int)):
        issues.append(f"Unexpected ser_no type: {type(ser_no)}")
    
    # Check reg_no structure
    reg_no = trademark_info.get('reg_no')
    if reg_no is None:
        issues.append("Missing reg_no field")
    elif isinstance(reg_no, list):
        if len(reg_no) == 0:
            issues.append("Empty reg_no list")
        elif len(reg_no) > 1:
            issues.append(f"Multiple reg_no values: {reg_no}")
    elif not isinstance(reg_no, (str, int)):
        issues.append(f"Unexpected reg_no type: {type(reg_no)}")
    
    # Check for expected fields
    expected_fields = ['full_filename', 'int_cls_list', 'trademark_text']
    for field in expected_fields:
        if field not in trademark_info:
            issues.append(f"Missing {field} field")
    
    # Log issues if any found
    if issues:
        log_structure_issue(
            f"STRUCTURE_ISSUES ({len(issues)} issues)", 
            docket, 
            {
                'image_name': image_name,
                'issues': issues,
                'trademark_data': trademark_info
            }
        )
    
    return issues

def find_missing_trademarks_optimized():
    """Find trademarks missing from PostgreSQL database with structure logging"""
    
    print("=== Trademark Missing Checker ===")
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Clear previous log file
    with open("structure_issues.log", "w") as f:
        f.write(f"Trademark Structure Issues Log - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write("=" * 80 + "\n\n")
    
    # Get cases from GZ database
    print("Fetching cases from GZ database...")
    df = get_table_from_GZ("tb_case", force_refresh=True)
    print(f"Loaded {len(df)} cases")
    
    # Get existing trademarks from PostgreSQL database (optimized approach)
    print("Fetching existing trademarks from PostgreSQL database...")
    db_trademarks_df = get_table_from_db("trademarks")
    
    # Create a set of existing ser_no values for fast lookup
    existing_ser_nos = set()
    if not db_trademarks_df.empty:
        existing_ser_nos = set(db_trademarks_df['ser_no'].dropna().astype(str))
    
    print(f"Found {len(existing_ser_nos)} existing trademarks in database")
    
    # Initialize counters and lists
    missing_count = 0
    missing_trademarks = []
    structure_issues_count = 0
    processed_cases = 0
    total_trademark_entries = 0
    
    print("Processing case data...")
    
    # Process each case
    for _, row in df.iterrows():
        processed_cases += 1
        
        # Progress reporting
        if processed_cases % 1000 == 0:
            print(f"Processed {processed_cases}/{len(df)} cases...")
        
        if not row['images'] or not isinstance(row['images'], dict):
            log_structure_issue("NO_IMAGES_DICT", row.get('docket', 'Unknown'), 
                              {'images': row.get('images')})
            continue
            
        # Get trademarks from images
        trademarks = row['images'].get('trademarks', {})
        if not trademarks:
            continue
        
        if not isinstance(trademarks, dict):
            log_structure_issue("TRADEMARKS_NOT_DICT", row.get('docket', 'Unknown'), 
                              {'trademarks': trademarks})
            continue
            
        # Process each trademark
        for image_name, trademark_info in trademarks.items():
            total_trademark_entries += 1
            
            if not isinstance(trademark_info, dict):
                log_structure_issue("TRADEMARK_INFO_NOT_DICT", row.get('docket', 'Unknown'), 
                                  {'image_name': image_name, 'trademark_info': trademark_info})
                continue
            
            # Analyze structure and log issues
            issues = analyze_trademark_structure(trademark_info, row.get('docket', 'Unknown'), image_name)
            if issues:
                structure_issues_count += 1
                
            # Get serial numbers - handle both single values and arrays
            ser_numbers = trademark_info.get('ser_no', [])
            if not isinstance(ser_numbers, list):
                ser_numbers = [ser_numbers] if ser_numbers else []
            
            # Get registration numbers - handle both single values and arrays
            reg_numbers = trademark_info.get('reg_no', [])
            if not isinstance(reg_numbers, list):
                reg_numbers = [reg_numbers] if reg_numbers else []
            
            # Process each serial number
            for ser_no in ser_numbers:
                if not ser_no:  # Skip empty/null serial numbers
                    continue
                    
                # Clean up serial number
                ser_no = str(ser_no).strip()
                if not ser_no:
                    continue
                
                # Check if trademark exists in database (fast set lookup)
                if ser_no not in existing_ser_nos:
                    missing_count += 1
                    missing_trademarks.append({
                        'plaintiff_id': row.get('plaintiff_id', 'Unknown'),
                        'docket': row.get('docket', 'Unknown'),
                        'ser_no': ser_no,
                        'reg_no': reg_numbers[0] if reg_numbers else None,
                        'trademark_text': trademark_info.get('trademark_text', ''),
                        'int_cls_list': str(trademark_info.get('int_cls_list', [])),
                        'image_filename': image_name,
                        'has_structure_issues': len(issues) > 0
                    })
                    
                    # Print progress for missing trademarks
                    if missing_count % 100 == 0:
                        print(f"Found {missing_count} missing trademarks so far...")

    # Print summary
    print(f"\n=== SUMMARY ===")
    print(f"Processed cases: {processed_cases}")
    print(f"Total trademark entries: {total_trademark_entries}")
    print(f"Structure issues found: {structure_issues_count}")
    print(f"Missing trademarks: {missing_count}")
    
    # Save missing trademarks to CSV
    if missing_trademarks:
        df_missing = pd.DataFrame(missing_trademarks)
        output_file = 'missing_trademarks_detailed.csv'
        df_missing.to_csv(output_file, index=False)
        print(f"\nMissing trademarks saved to: {output_file}")
        print(f"Structure issues log saved to: structure_issues.log")
        
        print(f"\nSample of missing trademarks:")
        print(df_missing.head(10))
        
        # Additional statistics
        print(f"\nStatistics:")
        print(f"- Unique plaintiff_ids: {df_missing['plaintiff_id'].nunique()}")
        print(f"- Unique dockets: {df_missing['docket'].nunique()}")
        print(f"- Missing with structure issues: {df_missing['has_structure_issues'].sum()}")
    else:
        print("No missing trademarks found!")
    
    print(f"\nCompleted at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    return missing_trademarks

if __name__ == "__main__":
    find_missing_trademarks_optimized()

import pandas as pd
import json
from datetime import datetime
from DatabaseManagement.ImportExport import get_table_from_GZ
from IP.Trademarks_Bulk.trademark_db import get_table_from_db

def log_case_structure_issues(case_issues, log_file="structure_issues.log"):
    if not case_issues:
        return

    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    with open(log_file, "a", encoding="utf-8") as f:
        f.write(f"[{timestamp}] CASE STRUCTURE ISSUES\n")
        f.write(f"Case ID: {case_issues['case_id']}\n")
        f.write(f"Docket: {case_issues['docket']}\n")
        f.write(f"Plaintiff ID: {case_issues['plaintiff_id']}\n")
        f.write(f"Total trademark issues in this case: {len(case_issues['trademark_issues'])}\n")
        f.write("-" * 60 + "\n")

        for i, trademark_issue in enumerate(case_issues['trademark_issues'], 1):
            f.write(f"  Trademark {i}: {trademark_issue['image_name']}\n")
            for issue in trademark_issue['issues']:
                f.write(f"    - {issue}\n")
            if trademark_issue.get('trademark_data'):
                f.write(f"    Data: {json.dumps(trademark_issue['trademark_data'], indent=6, default=str)}\n")
            f.write("\n")

        f.write("=" * 80 + "\n\n")

def analyze_trademark_structure(trademark_info, image_name):
    """Analyze trademark data structure problems and return issues list"""
    issues = []
    if 'ser_no' not in trademark_info:
        issues.append("Missing ser_no key")
    else:
        ser_no = trademark_info.get('ser_no')
        if not isinstance(ser_no, (list, str, int, type(None))):
            issues.append(f"Unexpected ser_no type: {type(ser_no)}")

    if 'reg_no' not in trademark_info:
        issues.append("Missing reg_no key")
    else:
        reg_no = trademark_info.get('reg_no')
        if not isinstance(reg_no, (list, str, int, type(None))):
            issues.append(f"Unexpected reg_no type: {type(reg_no)}")

    expected_keys = ['full_filename', 'int_cls', 'trademark_text']
    for key in expected_keys:
        if key not in trademark_info:
            issues.append(f"Missing {key} key")

    if not isinstance(trademark_info, dict):
        issues.append(f"Trademark info is not a dictionary: {type(trademark_info)}")

    return issues

def find_missing_trademarks():
    
    print("=== Trademark Missing Checker ===")
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # # Clear previous log file
    # with open("structure_issues.log", "w") as f:
    #     f.write(f"Trademark ACTUAL Structure Issues Log - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
    #     f.write("=" * 80 + "\n\n")
    
    # Get cases from GZ database
    print("Fetching cases from GZ database...")
    df = get_table_from_GZ("tb_case", force_refresh=True)
    print(f"Loaded {len(df)} cases")
    
    # Get existing trademarks from PostgreSQL database
    print("Fetching existing trademarks from PostgreSQL database...")
    db_trademarks_df = get_table_from_db("trademarks")

    # Create a set of existing reg_no values for fast lookup
    existing_ser_nos = set()
    if not db_trademarks_df.empty:
        existing_ser_nos = set(db_trademarks_df['ser_no'].dropna().astype(str))

    print(f"Found {len(existing_ser_nos)} existing trademarks in database")
    
    missing_count = 0
    missing_trademarks = []
    structure_issues_count = 0
    processed_cases = 0
    total_trademark_entries = 0
    
    print("Processing case data...")
    
    # Process each case
    for _, row in df.iterrows():
        processed_cases += 1

        # Progress reporting
        # if processed_cases % 1000 == 0:
        #     print(f"Processed {processed_cases}/{len(df)} cases...")

        # # Initialize case-level structure issues tracking
        # case_issues = {
        #     'case_id': row.get('id', 'Unknown'),
        #     'docket': row.get('docket', 'Unknown'),
        #     'plaintiff_id': row.get('plaintiff_id', 'Unknown'),
        #     'trademark_issues': []
        # }

        # if not row['images'] or not isinstance(row['images'], dict):
        #     case_issues['trademark_issues'].append({
        #         'image_name': 'N/A - No images dict',
        #         'issues': ['Missing or invalid images dictionary'],
        #         'trademark_data': {'images': row.get('images')}
        #     })
        #     log_case_structure_issues(case_issues)
        #     continue

        # Get trademarks from images
        trademarks =  (row.get('images') or {}).get('trademarks', {})
        if not trademarks:
            continue

        # if not isinstance(trademarks, dict):
        #     case_issues['trademark_issues'].append({
        #         'image_name': 'N/A - Trademarks not dict',
        #         'issues': ['Trademarks field is not a dictionary'],
        #         'trademark_data': {'trademarks': trademarks}
        #     })
        #     log_case_structure_issues(case_issues)
        #     continue

        # Process each trademark
        for image_name, trademark_info in trademarks.items():
            total_trademark_entries += 1

            # if not isinstance(trademark_info, dict):
            #     case_issues['trademark_issues'].append({
            #         'image_name': image_name,
            #         'issues': ['Trademark info is not a dictionary'],
            #         'trademark_data': trademark_info
            #     })
            #     continue

            # # Analyze structure and collect issues
            # issues = analyze_trademark_structure(trademark_info, image_name)
            # if issues:
            #     structure_issues_count += 1
            #     case_issues['trademark_issues'].append({
            #         'image_name': image_name,
            #         'issues': issues,
            #         'trademark_data': trademark_info
            #     })

            # Get registration numbers - handle both single values and arrays
            reg_numbers = trademark_info.get('reg_no', [])
            if not isinstance(reg_numbers, list):
                reg_numbers = [reg_numbers] if reg_numbers else []

            # Get serial numbers for additional info
            ser_numbers = trademark_info.get('ser_no', [])
            if not isinstance(ser_numbers, list):
                ser_numbers = [ser_numbers] if ser_numbers else []

            # Process each registration number (changed from ser_no to reg_no)
            for ser_no in ser_numbers:
                if not ser_no:  # Skip empty/null registration numbers
                    continue

                # Clean up registration number
                ser_no = str(ser_no).strip()
                if not ser_no:
                    continue

                # Check if trademark exists in database (fast set lookup)
                if ser_no not in existing_ser_nos:
                    missing_count += 1
                    missing_trademarks.append({
                        'plaintiff_id': row.get('plaintiff_id', 'Unknown'),
                        'docket': row.get('docket', 'Unknown'),
                        'case_id': row.get('id', 'Unknown'),
                        'reg_no': reg_no,
                        'ser_no': ser_numbers[0] if ser_numbers else None,
                        'trademark_text': trademark_info.get('trademark_text', ''),
                        'int_cls_list': str(trademark_info.get('int_cls_list', [])),
                        'image_filename': image_name,
                        # 'has_structure_issues': len(issues) > 0
                    })

                    # Print progress for missing trademarks
                    if missing_count % 100 == 0:
                        print(f"Found {missing_count} missing trademarks so far...")

        # Log all structure issues for this case if any found
        # if case_issues['trademark_issues']:
        #     log_case_structure_issues(case_issues)

    # Print summary
    print(f"\n=== SUMMARY ===")
    print(f"Processed cases: {processed_cases}")
    print(f"Total trademark entries: {total_trademark_entries}")
    print(f"Structure issues found: {structure_issues_count}")
    print(f"Missing trademarks: {missing_count}")
    
    # Save missing trademarks to CSV
    if missing_trademarks:
        df_missing = pd.DataFrame(missing_trademarks)
        output_file = 'missing_trademarks_ser_no.csv'
        df_missing.to_csv(output_file, index=False)
        print(f"\nMissing trademarks saved to: {output_file}")
        print(f"Structure issues log saved to: structure_issues.log")
        
        print(f"\nSample of missing trademarks:")
        print(df_missing.head(10))
        
        # Additional statistics
        # print(f"\nStatistics:")
        # print(f"- Unique plaintiff_ids: {df_missing['plaintiff_id'].nunique()}")
        # print(f"- Unique dockets: {df_missing['docket'].nunique()}")
        # print(f"- Missing with structure issues: {df_missing['has_structure_issues'].sum()}")
    else:
        print("No missing trademarks found!")
    
    print(f"\nCompleted at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    return missing_trademarks

if __name__ == "__main__":
    find_missing_trademarks()

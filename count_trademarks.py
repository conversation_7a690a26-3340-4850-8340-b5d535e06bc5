#!/usr/bin/env python3
from DatabaseManagement.ImportExport import get_table_from_GZ

def count_trademarks():
    df = get_table_from_GZ("tb_case", force_refresh=True)

    total_ser_nos = 0
    total_reg_nos = 0

    for _, row in df.iterrows():
        if row['images'] and isinstance(row['images'], dict):
            trademarks = row['images'].get('trademarks', {})
            if trademarks and isinstance(trademarks, dict):
                for trademark_info in trademarks.values():
                    if isinstance(trademark_info, dict):
                        # Count ser_nos
                        ser_nos = trademark_info.get('ser_no', [])
                        if not isinstance(ser_nos, list):
                            ser_nos = [ser_nos] if ser_nos else []
                        total_ser_nos += len([s for s in ser_nos if s])

                        # Count reg_nos
                        reg_nos = trademark_info.get('reg_no', [])
                        if not isinstance(reg_nos, list):
                            reg_nos = [reg_nos] if reg_nos else []
                        total_reg_nos += len([r for r in reg_nos if r])

    print(f"Total ser_nos: {total_ser_nos}")
    print(f"Total reg_nos: {total_reg_nos}")

if __name__ == "__main__":
    count_trademarks()
